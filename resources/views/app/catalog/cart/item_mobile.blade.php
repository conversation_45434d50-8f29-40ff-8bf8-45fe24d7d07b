
<!-- Mobile card layout (hidden on desktop) -->
<div class="mobile-cart-item d-md-none" id="mobile-{{$item->id}}">
    <!-- Mobile cart header with product info and delete button -->
    <div class="mobile-cart-header">
        <img src="{{ $item->product->cart_img }}" alt="{{ $item->product->name }}" class="mobile-cart-image">

        <div class="mobile-cart-product-info">
            <h6 class="mobile-cart-product-name">
                <a href="{{ route('detail', ['path' => $item->product->category->url, 'product' => $item->product]) }}">
                    {{ $item->product->name }}
                </a>
            </h6>
        </div>

        <button type="button" class="mobile-cart-delete remove-from-cart" data-delete-url="{{ route('cart.delete', ['product' => $item->product]) }}">
            <span></span>
            <span></span>
        </button>
    </div>

    <!-- Mobile cart controls - price, quantity, total -->
    <div class="mobile-cart-controls">
        <div class="mobile-price">
            @if ($item->discount)
                <span class="original-price text-decoration-line-through text-muted">{{ number_format($item->product->price, 2, '.', '') }} zł</span>
                <span class="text-success">{{ number_format($item->product->discounted_price, 2, '.', '') }} zł</span>
            @else
                <span>{{ number_format($item->product->price, 2, '.', '') }} zł</span>
            @endif
        </div>

        <div class="mobile-qty-control qty" data-id="{{ $item->id }}" data-update-url="{{ route('cart.update', ['product' => $item->product]) }}">
            <span class="qty-subtract">
                <i class="fa fa-minus"></i>
            </span>
            <input type="number" name="qty" value="{{ $item->quantity }}" data-id="{{ $item->id }}">
            <span class="qty-add">
                <i class="fa fa-plus"></i>
            </span>
        </div>

        <div class="mobile-total">
            @if ($item->discount)
                <div class="text-decoration-line-through text-muted small">{{ number_format($item->original_subtotal, 2) }} zł</div>
                <strong class="text-success">{{ number_format($item->subtotal, 2) }} zł</strong>
            @else
                <strong>{{ number_format($item->subtotal, 2) }} zł</strong>
            @endif
        </div>
    </div>
</div>
