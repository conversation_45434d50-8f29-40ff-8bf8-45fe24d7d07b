<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use App\Models\DeliveryMethod;

class CreateOrderRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Anyone can create an order
    }

    public function rules(): array
    {
        $rules = [
            'payment_method' => 'required|in:payu,transfer,cash',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:255',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'regulamen' => 'required|accepted'
        ];

        // Check if user is authenticated and has saved delivery addresses
        $hasAddresses = false;
        if (Auth::check()) {
            $hasAddresses = Auth::user()->deliveryAddresses()->exists();
            
            // If user has addresses, delivery_address_choice is required
            if ($hasAddresses) {
                $rules['delivery_address_choice'] = 'required';
            }
        }

        // If using existing address, validate it exists but don't require delivery method
        if ($this->delivery_address_choice && $this->delivery_address_choice !== 'new') {
            $rules['delivery_address_choice'] = 'required|exists:delivery_addresses,id';
        } else {
            // Only require delivery method for new addresses
            $rules['delivery_method_id'] = 'required|exists:delivery_methods,id';
            
            // For point delivery type, require point_id and point_address
            if ($this->delivery_method_type === 'point') {
                $rules['point_id'] = 'required|string';
                $rules['point_address'] = 'required|string';
            }
            
            // Validate new address fields - required if not using saved address
            // (either not authenticated or no delivery_address_choice provided)
            if (!$this->delivery_address_choice || $this->delivery_address_choice === 'new') {
                $rules['new_address.name'] = 'required';
                $rules['new_address.street'] = 'required';
                $rules['new_address.building_number'] = 'required';
                $rules['new_address.post_code'] = 'required';
                $rules['new_address.city'] = 'required';
            }

            // Optional fields for logged-in users
            if (Auth::check()) {
                $rules['save_address'] = 'boolean';
                $rules['make_default'] = 'boolean';
            }
        }

        // Add billing address validation if different billing address is selected
        if ($this->has('different_billing') && $this->different_billing == '1') {
            $rules['billing_street'] = 'required|string|max:255';
            $rules['billing_building_number'] = 'required|string|max:255';
            $rules['billing_post_code'] = 'required|string|max:255';
            $rules['billing_city'] = 'required|string|max:255';
            $rules['billing_name'] = 'nullable|string|max:255';
            $rules['billing_apartment_number'] = 'nullable|string|max:255';
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            'payment_method.required' => 'Wybierz metodę płatności',
            'delivery_method_id.required' => 'Wybierz metodę dostawy',
            'email.required' => 'Podaj adres email',
            'email.email' => 'Podaj prawidłowy adres email',
            'phone.required' => 'Podaj numer telefonu',
            'first_name.required' => 'Podaj imię',
            'last_name.required' => 'Podaj nazwisko',
            'new_address.name.required' => 'Podaj nazwę adresu',
            'new_address.street.required' => 'Podaj ulicę',
            'new_address.building_number.required' => 'Podaj numer budynku',
            'new_address.post_code.required' => 'Podaj kod pocztowy',
            'new_address.city.required' => 'Podaj miasto',
            'point_id.required' => 'Wybierz paczkomat',
            'point_address.required' => 'Adres paczkomatu jest wymagany',
            'delivery_address_choice.exists' => 'Wybrany adres nie istnieje',
            'regulamen.required' => 'Musisz zaakceptować regulamin',
            'regulamen.accepted' => 'Musisz zaakceptować regulamin',
            'billing_street.required' => 'Podaj ulicę do faktury',
            'billing_building_number.required' => 'Podaj numer budynku do faktury',
            'billing_post_code.required' => 'Podaj kod pocztowy do faktury',
            'billing_city.required' => 'Podaj miasto do faktury',
            'billing_name.string' => 'Nazwa adresu musi być tekstem',
            'billing_apartment_number.string' => 'Numer lokalu musi być tekstem',
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'is_logged_in' => Auth::check(),
            'delivery_method_type' => DeliveryMethod::find($this->delivery_method_id)?->type
        ]);
    }
} 