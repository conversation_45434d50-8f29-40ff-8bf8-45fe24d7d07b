<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Discount extends Model
{
    use HasFactory;

    protected $fillable = [
        'percentage',
        'start_date',
        'end_date',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    protected static $filters = [
        'percentage' => ['type' => 'numberrange'],
        'start_date' => ['type' => 'daterange'],
        'end_date' => ['type' => 'daterange'],
        'created_at' => ['type' => 'daterange'],
    ];

    protected static $sorts = [
        'percentage',
        'start_date',
        'end_date',
        'created_at',
    ];

    public function products()
    {
        return $this->belongsToMany(Product::class);
    }

    public function getPercentAttribute()
    {
        return number_format($this->percentage, 0);
    }

    public static function getFilters()
    {
        return static::$filters;
    }

    public static function getSorts()
    {
        return static::$sorts;
    }

    public function isActive()
    {
        $now = now();
        return $now >= $this->start_date && ($this->end_date === null || $now <= $this->end_date);
    }

    public function getStatusAttribute()
    {
        if ($this->isActive()) {
            return 'active';
        } elseif (now() < $this->start_date) {
            return 'scheduled';
        } else {
            return 'expired';
        }
    }
}
