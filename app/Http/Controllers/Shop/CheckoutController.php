<?php

namespace App\Http\Controllers\Shop;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateOrderRequest;
use App\Services\App\OrderService;
use App\Services\App\CartService;
use App\Services\PayuService;
use App\Models\Order;
use App\Models\DeliveryMethod;
use App\Services\App\DeliveryAddressService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CheckoutController extends Controller
{
    protected $orderService;
    protected $payuService;
    protected $deliveryAddressService;

    public function __construct(
        OrderService $orderService, 
        PayuService $payuService,
        DeliveryAddressService $deliveryAddressService
    ) {
        $this->orderService = $orderService;
        $this->payuService = $payuService;
        $this->deliveryAddressService = $deliveryAddressService;
    }

    public function index(CartService $cartService)
    {
        $cart = $cartService->getCart();
        
        if (!$cart || $cart->items->isEmpty()) {
            return redirect()->route('cart.index')->with('error', 'Twój koszyk jest pusty');
        }

        $deliveryMethods = DeliveryMethod::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        $subTotal = $cartService->getSubtotal(); // Original prices
        $cartTotal = $cartService->getTotal(); // Discounted prices
        $discount = $cartService->getTotalDiscount(); // Total discount amount
        $discount_percent = $discount > 0 ? round(($discount / $subTotal) * 100) : 0;

        // Get delivery info - if no method selected, use default/first available method for calculation
        $selectedMethodId = session('delivery_method_id');
        if (!$selectedMethodId && $deliveryMethods->isNotEmpty()) {
            $selectedMethodId = $deliveryMethods->first()->id;
        }

        $deliveryInfo = $cartService->getDeliveryCostInfo($selectedMethodId);
        $deliveryCost = $deliveryInfo['cost'] ?? 0; // Use parcel-based delivery cost
        $total = $cartTotal + $deliveryCost; // Calculate correct total with discounted prices + delivery
        $cartItems = $cart->items;
        $deliveryMethods = DeliveryMethod::where('is_active', true)->orderBy('sort_order')->get();
        $coupon = null;
        
        return view('app.pages.checkout', compact(
            'subTotal',
            'cartTotal',
            'total',
            'deliveryInfo',
            'discount',
            'discount_percent',
            'coupon',
            'cartItems',
            'deliveryMethods'
        ));
    }

    public function processOrder(CreateOrderRequest $request, CartService $cartService)
    {
        try {
            $cart = $cartService->getCart();
            
            if (!$cart || $cart->items->isEmpty()) {
                throw new \Exception('Koszyk jest pusty');
            }

            // Handle delivery address first
            $deliveryAddress = $this->deliveryAddressService->handleDeliveryAddress($request);

            // Prepare order data
            $orderData = [
                'user_id' => Auth::id(),
                'payment_method' => $request->input('payment_method', 'payu'),
                'delivery_method' => $deliveryAddress->deliveryMethod->name, // Use the method from the address
                'status' => 'pending',
                'payment_status' => 'pending',
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'phone' => $request->phone,
                'notes' => $request->notes
            ];

            // Add billing address if different from delivery address
            if ($request->has('different_billing') && $request->different_billing == '1') {
                $orderData['billing_address'] = [
                    'name' => $request->billing_name,
                    'street' => $request->billing_street,
                    'building_number' => $request->billing_building_number,
                    'apartment_number' => $request->billing_apartment_number,
                    'post_code' => $request->billing_post_code,
                    'city' => $request->billing_city,
                ];
            }

            // Create order
            $order = $this->orderService->createOrder($cart, $orderData, $deliveryAddress);

            // Handle PayU payment
            if ($request->input('payment_method') === 'payu') {
                $payuResponse = $this->payuService->createOrder([
                    'orderId' => $order->uuid,
                    'amount' => $order->total * 100,
                    'description' => "Zamówienie {$order->uuid}",
                    'customer' => [
                        'email' => $order->email,
                        'firstName' => $order->first_name,
                        'lastName' => $order->last_name,
                        'phone' => $order->phone,
                    ],
                ]);

                // Update order with PayU orderId
                $order->update([
                    'payment_status' => 'pending',
                    'payu_order_id' => $payuResponse['payuOrderId'] ?? null,
                    'payu_external_id' => $payuResponse['extOrderId'] ?? null
                ]);

                // Clear cart and redirect to payment page
                $cart->update(['status' => 'ordered']);
                return redirect($payuResponse['redirectUri']);
            }

            // For other payment methods
            $cart->update(['status' => 'ordered']);
            return redirect()->route('order.show', $order->uuid)
                ->with('success', 'Zamówienie zostało złożone pomyślnie');

        } catch (\Exception $e) {
            Log::error('Order processing error: ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->withErrors('Wystąpił błąd: ' . $e->getMessage());
        }
    }

    public function payuNotify(Request $request)
    {
        try {
            $orderData = $this->payuService->handleNotification($request);
            
            if ($orderData['status'] === 'COMPLETED') {
                $order = Order::where('uuid', $orderData['orderId'])->firstOrFail();
                
                // Update order status and send to Molos
                $this->orderService->completeOrder($order);
            }

            return response()->json(['status' => 'OK']);
        } catch (\Exception $e) {
            \Log::error('PayU notification error: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function continuePayment(Order $order)
    {
        // Handle post-payment redirect
        return redirect()->route('order.show', $order->uuid)
            ->with('success', 'Dziękujemy za złożenie zamówienia');
    }

    public function updateDeliveryMethod(Request $request, CartService $cartService)
    {
        $validatedData = $request->validate([
            'delivery_method_id' => 'required|exists:delivery_methods,id'
        ]);

        $deliveryMethod = DeliveryMethod::findOrFail($validatedData['delivery_method_id']);
        session(['delivery_method_id' => $deliveryMethod->id]);

        // Use the new parcel-based delivery calculation
        $deliveryInfo = $cartService->getDeliveryCostInfo($deliveryMethod->id);
        $subTotal = $cartService->getSubtotal(); // Original prices
        $cartTotal = $cartService->getTotal(); // Discounted prices
        $deliveryCost = $deliveryInfo['cost'] ?? 0; // Use the calculated cost from parcel system
        $total = $cartTotal + $deliveryCost; // Use discounted prices + delivery

        return response()->json([
            'success' => true,
            'totals' => [
                'subTotal' => $subTotal,
                'deliveryCost' => $deliveryCost,
                'total' => $total,
                'totalFormatted' => number_format($total, 2, ',', ' '),
                'deliveryHtml' => view('app.checkout.partials.delivery-cost', [
                    'deliveryInfo' => $deliveryInfo,
                    'deliveryMethod' => $deliveryMethod
                ])->render()
            ]
        ]);
    }

    public function updateDeliveryAddress(Request $request, CartService $cartService)
    {
        $validatedData = $request->validate([
            'delivery_address_id' => 'required|exists:delivery_addresses,id'
        ]);

        $deliveryAddress = \App\Models\DeliveryAddress::findOrFail($validatedData['delivery_address_id']);
        $deliveryMethod = $deliveryAddress->deliveryMethod;

        // Store the delivery method in session
        session(['delivery_method_id' => $deliveryMethod->id]);

        // Use the new parcel-based delivery calculation
        $deliveryInfo = $cartService->getDeliveryCostInfo($deliveryMethod->id);
        $subTotal = $cartService->getSubtotal(); // Original prices
        $cartTotal = $cartService->getTotal(); // Discounted prices
        $deliveryCost = $deliveryInfo['cost'] ?? 0;
        $total = $cartTotal + $deliveryCost; // Use discounted prices + delivery

        return response()->json([
            'success' => true,
            'delivery_method' => [
                'id' => $deliveryMethod->id,
                'name' => $deliveryMethod->name,
                'type' => $deliveryMethod->type,
                'api_provider' => $deliveryMethod->api_provider
            ],
            'totals' => [
                'subTotal' => $subTotal,
                'deliveryCost' => $deliveryCost,
                'total' => $total,
                'totalFormatted' => number_format($total, 2, ',', ' '),
                'deliveryHtml' => view('app.checkout.partials.delivery-cost', [
                    'deliveryInfo' => $deliveryInfo,
                    'deliveryMethod' => $deliveryMethod
                ])->render()
            ]
        ]);
    }
}
