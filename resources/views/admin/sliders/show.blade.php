@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
            <i class="bi bi-images me-2"></i>Slider Details
        </h2>
        <div class="flex space-x-2">
            <a href="{{ route('admin.sliders.edit', $slider) }}" class="bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
                <i class="bi bi-pencil me-1"></i>Edit Slider
            </a>
            <a href="{{ route('admin.sliders.index') }}" class="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
                <i class="bi bi-arrow-left me-1"></i>Back to List
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Basic Information -->
        <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-4">Basic Information</h3>
            
            <div class="space-y-3">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Title</label>
                    <p class="text-gray-900">{{ $slider->title }}</p>
                </div>

                @if($slider->link)
                <div>
                    <label class="block text-sm font-medium text-gray-700">Link</label>
                    <p class="text-blue-500">
                        <a href="{{ $slider->link }}" target="_blank" class="hover:text-blue-700">
                            <i class="bi bi-link-45deg me-1"></i>{{ $slider->link }}
                        </a>
                    </p>
                </div>
                @endif

                @if($slider->description)
                <div>
                    <label class="block text-sm font-medium text-gray-700">Description</label>
                    <p class="text-gray-900">{{ $slider->description }}</p>
                </div>
                @endif

                <div>
                    <label class="block text-sm font-medium text-gray-700">Status</label>
                    @if($slider->is_active ?? true)
                        @php
                            $now = now();
                            $isInDateRange = (!$slider->start_date || $now >= $slider->start_date) &&
                                           (!$slider->end_date || $now <= $slider->end_date);
                        @endphp
                        @if($isInDateRange)
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="bi bi-check-circle me-1"></i>Active
                            </span>
                        @else
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i class="bi bi-clock me-1"></i>Scheduled
                            </span>
                        @endif
                    @else
                        <span class="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <i class="bi bi-x-circle me-1"></i>Inactive
                        </span>
                    @endif
                </div>

                @if($slider->start_date || $slider->end_date)
                <div>
                    <label class="block text-sm font-medium text-gray-700">Schedule</label>
                    <div class="text-gray-900">
                        @if($slider->start_date && $slider->end_date)
                            {{ $slider->start_date ? $slider->start_date->format('d.m.Y H:i') : '' }} - {{ $slider->end_date ? $slider->end_date->format('d.m.Y H:i') : '' }}
                        @elseif($slider->start_date)
                            From: {{ $slider->start_date ? $slider->start_date->format('d.m.Y H:i') : '' }}
                        @elseif($slider->end_date)
                            Until: {{ $slider->end_date ? $slider->end_date->format('d.m.Y H:i') : '' }}
                        @endif
                    </div>
                </div>
                @endif

                <div>
                    <label class="block text-sm font-medium text-gray-700">Created</label>
                    <p class="text-gray-900">{{ $slider->created_at->format('d.m.Y H:i') }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">Last Updated</label>
                    <p class="text-gray-900">{{ $slider->updated_at->format('d.m.Y H:i') }}</p>
                </div>
            </div>
        </div>

        <!-- Images -->
        <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-4">Images</h3>
            
            <div class="space-y-4">
                <!-- Desktop Image -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Desktop Image</label>
                    @if($slider->hasMedia('image'))
                        <div class="border rounded-lg overflow-hidden">
                            <img src="{{ $slider->getFirstMediaUrl('image') }}"
                                 alt="{{ $slider->title }}"
                                 class="w-full h-auto">
                        </div>
                    @else
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                            <i class="bi bi-image text-gray-400 text-3xl"></i>
                            <p class="text-gray-500 mt-2">No desktop image uploaded</p>
                        </div>
                    @endif
                </div>

                <!-- Mobile Image -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Mobile Image</label>
                    @if($slider->hasMedia('mobile_image'))
                        <div class="border rounded-lg overflow-hidden max-w-xs">
                            <img src="{{ $slider->getFirstMediaUrl('mobile_image') }}"
                                 alt="{{ $slider->title }} Mobile"
                                 class="w-full h-auto">
                        </div>
                    @else
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center max-w-xs">
                            <i class="bi bi-phone text-gray-400 text-3xl"></i>
                            <p class="text-gray-500 mt-2">No mobile image uploaded</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
