@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
            <i class="bi bi-percent me-2"></i>Create Discount
        </h2>
        <a href="{{ route('admin.discounts.index') }}" class="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
            <i class="bi bi-arrow-left me-1"></i>Back to Discounts
        </a>
    </div>

    <form id="discount-form" action="{{ route('admin.discounts.store') }}" method="POST">
        @csrf
        <input type="hidden" name="product_ids" id="selected-product-ids">

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Discount Information -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">Discount Information</h3>

                <!-- Discount Fields -->
                <div class="mb-4">
                    <label for="percentage" class="block text-sm font-medium text-gray-700 mb-1">Discount Percentage (%)</label>
                    <input type="number"
                           id="percentage"
                           name="percentage"
                           class="w-full border rounded p-2 @error('percentage') border-red-500 @enderror"
                           min="0"
                           max="100"
                           step="0.01"
                           value="{{ old('percentage') }}"
                           required>
                    @error('percentage')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="mb-4">
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input type="datetime-local"
                           id="start_date"
                           name="start_date"
                           class="w-full border rounded p-2 @error('start_date') border-red-500 @enderror"
                           value="{{ old('start_date') }}"
                           required>
                    @error('start_date')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="mb-4">
                    <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input type="datetime-local"
                           id="end_date"
                           name="end_date"
                           class="w-full border rounded p-2 @error('end_date') border-red-500 @enderror"
                           value="{{ old('end_date') }}">
                    @error('end_date')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    <p class="text-gray-500 text-sm mt-1">Leave empty for no end date</p>
                </div>
            </div>

            <!-- Selected Products -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">
                    Selected Products
                    <span id="selected-count" class="text-sm font-normal text-gray-600">(0)</span>
                </h3>

                <div id="selected-products" class="space-y-2 max-h-96 overflow-y-auto">
                    <div id="no-products-message" class="text-center py-8 text-gray-500">
                        <i class="bi bi-box-seam text-3xl"></i>
                        <p class="mt-2">No products selected</p>
                        <p class="text-sm">Search and add products below</p>
                    </div>
                </div>
            </div>
        </div>

        <hr class="my-8">

        <!-- Add Products Section -->
        <div>
            <h3 class="text-lg font-semibold mb-4">Add Products to Discount</h3>

            <!-- Search Filter -->
            <div class="mb-4">
                <input type="text"
                       id="product-search"
                       class="w-full border rounded p-2"
                       placeholder="Search products by name or SKU...">
            </div>

            <!-- Available Products -->
            <div id="available-products" class="border rounded">
                <div class="bg-gray-50 px-4 py-2 border-b">
                    <strong>Available Products</strong>
                </div>
                <div id="products-list" class="max-h-96 overflow-y-auto">
                    <!-- Products will be loaded here via AJAX -->
                </div>
                <div id="products-pagination" class="px-4 py-2 border-t bg-gray-50">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-between mt-8">
            <a href="{{ route('admin.discounts.index') }}" class="bg-gray-300 text-gray-700 py-2 px-4 rounded hover:bg-gray-400">
                <i class="bi bi-x-circle me-1"></i>Cancel
            </a>
            <button type="submit" class="bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
                <i class="bi bi-check-circle me-1"></i>Create Discount
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    let selectedProducts = [];

    // Load initial products (we'll create a temporary discount ID of 0 for new discounts)
    loadProducts();

    // Search functionality
    let searchTimeout;
    document.getElementById('product-search').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            loadProducts(1, this.value);
        }, 300);
    });

    // Load products function
    function loadProducts(page = 1, search = '') {
        // For create form, we'll use a different approach since we don't have a discount ID yet
        const url = `/admin/products/search?q=${encodeURIComponent(search)}&page=${page}&exclude=${selectedProducts.join(',')}`;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                renderProducts(data);
                // For create form, we don't have pagination from the search endpoint, so we'll handle it differently
            })
            .catch(error => {
                console.error('Error loading products:', error);
            });
    }

    // Render products
    function renderProducts(products) {
        const container = document.getElementById('products-list');

        if (products.length === 0) {
            container.innerHTML = '<div class="px-4 py-8 text-center text-gray-500">No products found.</div>';
            return;
        }

        container.innerHTML = products.map(product => `
            <div class="px-4 py-3 border-b hover:bg-gray-50 flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <div class="h-12 w-12 bg-gray-100 rounded flex items-center justify-center">
                        <i class="bi bi-box-seam text-gray-400"></i>
                    </div>
                    <div>
                        <strong>${product.text}</strong>
                        <div class="text-sm text-gray-500">
                            ID: ${product.id}
                        </div>
                    </div>
                </div>
                <button type="button"
                        class="add-product-btn bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600"
                        data-product-id="${product.id}"
                        data-product-name="${product.text}">
                    <i class="bi bi-plus me-1"></i>Add
                </button>
            </div>
        `).join('');

        // Add event listeners to add buttons
        container.querySelectorAll('.add-product-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                addProductToSelection(this.dataset.productId, this.dataset.productName);
            });
        });
    }

    // Add product to selection
    function addProductToSelection(productId, productName) {
        if (selectedProducts.includes(productId)) {
            return; // Already selected
        }

        selectedProducts.push(productId);
        updateSelectedProductsDisplay();
        updateSelectedProductsInput();
        loadProducts(1, document.getElementById('product-search').value); // Refresh to remove from available
    }

    // Remove product from selection
    function removeProductFromSelection(productId) {
        selectedProducts = selectedProducts.filter(id => id !== productId);
        updateSelectedProductsDisplay();
        updateSelectedProductsInput();
        loadProducts(1, document.getElementById('product-search').value); // Refresh to add back to available
    }

    // Update selected products display
    function updateSelectedProductsDisplay() {
        const container = document.getElementById('selected-products');
        const countElement = document.getElementById('selected-count');
        const noProductsMessage = document.getElementById('no-products-message');

        countElement.textContent = `(${selectedProducts.length})`;

        if (selectedProducts.length === 0) {
            noProductsMessage.style.display = 'block';
            return;
        }

        noProductsMessage.style.display = 'none';

        // For simplicity in create form, we'll show basic product info
        // In a real implementation, you might want to fetch full product details
        container.innerHTML = selectedProducts.map(productId => `
            <div class="flex items-center justify-between p-3 bg-white rounded border">
                <div class="flex items-center space-x-3">
                    <div class="h-10 w-10 bg-gray-100 rounded flex items-center justify-center">
                        <i class="bi bi-box-seam text-gray-400"></i>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">Product ID: ${productId}</p>
                        <p class="text-sm text-gray-500">Selected for discount</p>
                    </div>
                </div>
                <button type="button"
                        class="text-red-500 hover:text-red-700 text-sm"
                        onclick="removeProductFromSelection('${productId}')"
                        title="Remove Product">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `).join('');
    }

    // Update hidden input with selected product IDs
    function updateSelectedProductsInput() {
        document.getElementById('selected-product-ids').value = selectedProducts.join(',');
    }

    // Make removeProductFromSelection available globally
    window.removeProductFromSelection = removeProductFromSelection;
});
</script>
@endpush
@endsection
