<div class="col-lg-6 cart-summary-container">
<table>
    <tbody>
    <tr class="razem">
        <th>Razem</th>
        <td>{{ $subTotal }} zł</td>
    </tr>
    
    <tr class="discount @if ($subTotal == $total)d-none @endif">
        <th>
            Rabat
            @if ($coupon)
                {{ $coupon->code }}
            @endif
        </th>
        <td> - {{ number_format($subTotal - $total, 2) }} zł</td>
    </tr>
    <tr class="total" id="delivery-td">
        <th>
            <h6 class="mb-0">Dostawa</h6>
        </th>
        <td> 
            @if($deliveryInfo['is_free'])
                <strong class="text-success">DARMOWA DOSTAWA</strong>
            @else
                <strong>
                    od {{ number_format($deliveryInfo['min_cost'], 2) }} zł
                </strong>
                @if($deliveryInfo['free_shipping_threshold'] > 0)
                    <br>
                    <small class="text-muted">
                        @if($deliveryInfo['amount_left_for_free'] > 0)
                            Dodaj produkty za {{ number_format($deliveryInfo['amount_left_for_free'], 2) }} zł, 
                            aby otrzymać darmową dostawę
                        @endif
                    </small>
                @endif
            @endif
        </td>
    </tr>

    <tr class="total" id="total-td">
        <th>
            <h6 class="mb-0">Do zapłaty</h6>
        </th>
        <td> 
            <strong>
                {{ number_format($total, 2) }} zł
            </strong>
            <br>
            <small class="text-muted">+ koszt dostawy</small>
        </td>
    </tr>
    </tbody>
</table>
<a href="{{ route('checkout') }}" class="andro_btn-custom primary btn-block">Zamawiam</a>
<input type="hidden" name="total_sum" value="{{ $total }}">
</div>