<div class="cart-summary mb-4">
    <h4 class="mb-3">Podsumowanie zamówienia</h4>
    
    <div class="summary-item d-flex justify-content-between mb-2">
        <span>Suma</span>
        <strong>{{ number_format($subTotal, 2, ',', ' ') }} zł</strong>
    </div>

    <div class="summary-item d-flex justify-content-between mb-2" id="delivery-cost-section">
        <span>Koszt dostawy</span>
        <div class="text-end">
            <div>Koszty przesyłki</div>
            @if(!session('delivery_method_id'))
                @if($deliveryInfo['is_free'])
                    <strong class="text-success">Dostawa gratis</strong>
                @else
                    <strong>od {{ number_format($deliveryInfo['min_cost'], 2, ',', ' ') }} zł</strong>
                @endif
            @else
                @php
                    $selectedMethod = $deliveryMethods->find(session('delivery_method_id'));
                @endphp
                @if($subTotal >= $selectedMethod->min_cart_amount && $selectedMethod->min_cart_amount > 0)
                    <strong class="text-success">Dostawa gratis</strong>
                @else
                    <strong>{{ number_format($selectedMethod->price, 2, ',', ' ') }} zł</strong>
                @endif
            @endif
        </div>
    </div>

    @if($discount > 0)
    <div class="summary-item d-flex justify-content-between mb-2">
        <span>Kupon / Rabat</span>
        <div class="text-end">
            @if($coupon)
                <div>{{ $coupon->code }} ({{ $coupon->discount_percent }}% zniżki)</div>
            @else
                <div>{{ $discount_percent }}% zniżki</div>
            @endif
            <strong class="text-danger">-{{ number_format($discount, 2, ',', ' ') }} zł</strong>
        </div>
    </div>
    @endif

    <div class="coupon-form mb-3">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="Wpisz kod kuponu" name="coupon_code">
            <button class="btn btn-outline-secondary" type="button" onclick="applyCoupon()">Zastosuj</button>
        </div>
    </div>

    <div class="summary-total d-flex justify-content-between mt-3 pt-3 border-top">
        <div>
            <strong>Cena razem</strong>
            <div class="small text-muted">z VAT</div>
        </div>
        <strong class="h4 mb-0" id="total-price">{{ number_format($total, 2, ',', ' ') }} zł</strong>
    </div>
</div>

<style>
.cart-summary {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
}

.summary-item {
    color: #6c757d;
}

.summary-total {
    color: #212529;
}

.coupon-form .input-group {
    border: 1px dashed #dee2e6;
    border-radius: 0.25rem;
    overflow: hidden;
}

.coupon-form .form-control {
    border: none;
}

.coupon-form .btn {
    border: none;
    background: #f8f9fa;
}

.coupon-form .btn:hover {
    background: #e9ecef;
}
</style> 