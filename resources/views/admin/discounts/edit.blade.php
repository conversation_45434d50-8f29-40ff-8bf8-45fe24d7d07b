@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
            <i class="bi bi-percent me-2"></i>Edit Discount
        </h2>
        <div class="flex space-x-2">
            <a href="{{ route('admin.discounts.show', $discount) }}" class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                <i class="bi bi-eye me-1"></i>View Discount
            </a>
            <a href="{{ route('admin.discounts.index') }}" class="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
                <i class="bi bi-arrow-left me-1"></i>Back to Discounts
            </a>
        </div>
    </div>

    <form action="{{ route('admin.discounts.update', $discount->id) }}" method="POST">
        @csrf
        @method('PUT')

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Discount Information -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">Discount Information</h3>

                <!-- Discount Fields -->
                <div class="mb-4">
                    <label for="percentage" class="block text-sm font-medium text-gray-700 mb-1">Discount Percentage (%)</label>
                    <input type="number"
                           id="percentage"
                           name="percentage"
                           class="w-full border rounded p-2 @error('percentage') border-red-500 @enderror"
                           min="0"
                           max="100"
                           step="0.01"
                           value="{{ old('percentage', $discount->percentage) }}"
                           required>
                    @error('percentage')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="mb-4">
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input type="datetime-local"
                           id="start_date"
                           name="start_date"
                           class="w-full border rounded p-2 @error('start_date') border-red-500 @enderror"
                           value="{{ old('start_date', $discount->start_date->format('Y-m-d\TH:i')) }}"
                           required>
                    @error('start_date')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="mb-4">
                    <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input type="datetime-local"
                           id="end_date"
                           name="end_date"
                           class="w-full border rounded p-2 @error('end_date') border-red-500 @enderror"
                           value="{{ old('end_date', optional($discount->end_date)->format('Y-m-d\TH:i')) }}">
                    @error('end_date')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                    <p class="text-gray-500 text-sm mt-1">Leave empty for no end date</p>
                </div>
            </div>

            <!-- Current Products -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">
                    Current Products
                    <span class="text-sm font-normal text-gray-600">({{ $discount->products->count() }})</span>
                </h3>

                @if($discount->products->count() > 0)
                    <div class="space-y-2 max-h-96 overflow-y-auto">
                        @foreach($discount->products as $product)
                            <div class="flex items-center justify-between p-3 bg-white rounded border">
                                <div class="flex items-center space-x-3">
                                    @if($product->hasMedia('images'))
                                        <img src="{{ $product->getFirstMediaUrl('images', 'thumb') }}"
                                             alt="{{ $product->name }}"
                                             class="h-12 w-12 object-cover rounded">
                                    @else
                                        <div class="h-12 w-12 bg-gray-100 rounded flex items-center justify-center">
                                            <i class="bi bi-box-seam text-gray-400"></i>
                                        </div>
                                    @endif
                                    <div>
                                        <p class="font-medium text-gray-900">{{ $product->name }}</p>
                                        <p class="text-sm text-gray-500">SKU: {{ $product->sku ?? 'N/A' }}</p>
                                        <p class="text-sm text-green-600">
                                            {{ number_format($product->price, 2) }} PLN →
                                            {{ number_format($product->price * (100 - $discount->percentage) / 100, 2) }} PLN
                                        </p>
                                    </div>
                                </div>
                                <button type="button"
                                        class="remove-product-btn text-red-500 hover:text-red-700 text-sm"
                                        data-product-id="{{ $product->id }}"
                                        title="Remove Product">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8 text-gray-500">
                        <i class="bi bi-box-seam text-3xl"></i>
                        <p class="mt-2">No products assigned to this discount</p>
                        <p class="text-sm">Search and add products below</p>
                    </div>
                @endif
            </div>
        </div>

        <hr class="my-8">

        <!-- Add Products Section -->
        <div>
            <h3 class="text-lg font-semibold mb-4">Add Products to Discount</h3>

            <!-- Search Filter -->
            <div class="mb-4">
                <input type="text"
                       id="product-search"
                       class="w-full border rounded p-2"
                       placeholder="Search products by name or SKU...">
            </div>

            <!-- Available Products -->
            <div id="available-products" class="border rounded">
                <div class="bg-gray-50 px-4 py-2 border-b">
                    <strong>Available Products</strong>
                </div>
                <div id="products-list" class="max-h-96 overflow-y-auto">
                    <!-- Products will be loaded here via AJAX -->
                </div>
                <div id="products-pagination" class="px-4 py-2 border-t bg-gray-50">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-between mt-8">
            <a href="{{ route('admin.discounts.index') }}" class="bg-gray-300 text-gray-700 py-2 px-4 rounded hover:bg-gray-400">
                <i class="bi bi-x-circle me-1"></i>Cancel
            </a>
            <button type="submit" class="bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
                <i class="bi bi-check-circle me-1"></i>Update Discount
            </button>
        </div>
    </form>
</div>
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const discountId = {{ $discount->id }};
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Load initial products
    loadProducts();

    // Search functionality
    let searchTimeout;
    document.getElementById('product-search').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            loadProducts(1, this.value);
        }, 300);
    });

    // Load products function
    function loadProducts(page = 1, search = '') {
        const url = `/admin/discounts/${discountId}/search-products?page=${page}&search=${encodeURIComponent(search)}`;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                renderProducts(data.products);
                renderPagination(data.pagination, search);
            })
            .catch(error => {
                console.error('Error loading products:', error);
            });
    }

    // Render products
    function renderProducts(products) {
        const container = document.getElementById('products-list');

        if (products.length === 0) {
            container.innerHTML = '<div class="px-4 py-8 text-center text-gray-500">No products found.</div>';
            return;
        }

        container.innerHTML = products.map(product => `
            <div class="px-4 py-3 border-b hover:bg-gray-50 flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <div class="h-12 w-12 bg-gray-100 rounded flex items-center justify-center">
                        <i class="bi bi-box-seam text-gray-400"></i>
                    </div>
                    <div>
                        <strong>${product.name}</strong>
                        <div class="text-sm text-gray-500">
                            ID: ${product.id} | SKU: ${product.sku || 'N/A'} |
                            Category: ${product.category ? product.category.name : 'N/A'}
                        </div>
                        <div class="text-sm text-green-600">
                            Price: ${parseFloat(product.price).toFixed(2)} PLN
                        </div>
                    </div>
                </div>
                <button type="button"
                        class="add-product-btn bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600"
                        data-product-id="${product.id}">
                    <i class="bi bi-plus me-1"></i>Add
                </button>
            </div>
        `).join('');

        // Add event listeners to add buttons
        container.querySelectorAll('.add-product-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                addProductToDiscount(this.dataset.productId);
            });
        });
    }

    // Render pagination
    function renderPagination(pagination, search) {
        const container = document.getElementById('products-pagination');

        if (pagination.last_page <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHtml = '<div class="flex justify-between items-center">';
        paginationHtml += `<span class="text-sm text-gray-600">Page ${pagination.current_page} of ${pagination.last_page}</span>`;
        paginationHtml += '<div class="flex space-x-2">';

        if (pagination.current_page > 1) {
            paginationHtml += `<button type="button" class="pagination-btn bg-blue-500 text-white px-2 py-1 rounded text-sm" data-page="${pagination.current_page - 1}" data-search="${search}">Previous</button>`;
        }

        if (pagination.has_more) {
            paginationHtml += `<button type="button" class="pagination-btn bg-blue-500 text-white px-2 py-1 rounded text-sm" data-page="${pagination.current_page + 1}" data-search="${search}">Next</button>`;
        }

        paginationHtml += '</div></div>';
        container.innerHTML = paginationHtml;

        // Add event listeners to pagination buttons
        container.querySelectorAll('.pagination-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                loadProducts(this.dataset.page, this.dataset.search);
            });
        });
    }

    // Add product to discount
    function addProductToDiscount(productId) {
        fetch(`/admin/discounts/${discountId}/add-product`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({ product_id: productId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload(); // Reload to show updated product list
            } else {
                alert('Error adding product to discount');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error adding product to discount');
        });
    }

    // Remove product from discount
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-product-btn') || e.target.closest('.remove-product-btn')) {
            const btn = e.target.classList.contains('remove-product-btn') ? e.target : e.target.closest('.remove-product-btn');
            const productId = btn.dataset.productId;

            if (confirm('Remove this product from the discount?')) {
                fetch(`/admin/discounts/${discountId}/remove-product`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({ product_id: productId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload(); // Reload to show updated product list
                    } else {
                        alert('Error removing product from discount');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error removing product from discount');
                });
            }
        }
    });
});
</script>
@endpush
@endsection
