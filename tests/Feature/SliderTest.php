<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Slider;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class SliderTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_create_slider_with_is_active_true()
    {
        $sliderData = [
            'title' => 'Test Slider',
            'link' => 'https://example.com',
            'is_active' => '1', // Checkbox value when checked
            'start_date' => '2024-01-01',
            'end_date' => '2024-12-31',
        ];

        $response = $this->post(route('admin.sliders.store'), $sliderData);

        $response->assertRedirect(route('admin.sliders.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('sliders', [
            'title' => 'Test Slider',
            'link' => 'https://example.com',
            'is_active' => true, // Should be stored as boolean true
        ]);
    }

    /** @test */
    public function it_can_create_slider_with_is_active_false()
    {
        $sliderData = [
            'title' => 'Test Slider Inactive',
            'link' => 'https://example.com',
            'is_active' => '0', // Hidden input value when checkbox unchecked
            'start_date' => '2024-01-01',
            'end_date' => '2024-12-31',
        ];

        $response = $this->post(route('admin.sliders.store'), $sliderData);

        $response->assertRedirect(route('admin.sliders.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('sliders', [
            'title' => 'Test Slider Inactive',
            'link' => 'https://example.com',
            'is_active' => false, // Should be stored as boolean false
        ]);
    }

    /** @test */
    public function it_can_update_slider_with_is_active_true()
    {
        $slider = Slider::create([
            'title' => 'Original Slider',
            'link' => 'https://original.com',
            'is_active' => false,
        ]);

        $updateData = [
            'title' => 'Updated Slider',
            'link' => 'https://updated.com',
            'is_active' => '1', // Checkbox checked
        ];

        $response = $this->put(route('admin.sliders.update', $slider), $updateData);

        $response->assertRedirect(route('admin.sliders.index'));
        $response->assertSessionHas('success');

        $slider->refresh();
        $this->assertEquals('Updated Slider', $slider->title);
        $this->assertEquals('https://updated.com', $slider->link);
        $this->assertTrue($slider->is_active); // Should be boolean true
    }

    /** @test */
    public function it_can_update_slider_with_is_active_false()
    {
        $slider = Slider::create([
            'title' => 'Original Slider',
            'link' => 'https://original.com',
            'is_active' => true,
        ]);

        $updateData = [
            'title' => 'Updated Slider',
            'link' => 'https://updated.com',
            'is_active' => '0', // Hidden input value (checkbox unchecked)
        ];

        $response = $this->put(route('admin.sliders.update', $slider), $updateData);

        $response->assertRedirect(route('admin.sliders.index'));
        $response->assertSessionHas('success');

        $slider->refresh();
        $this->assertEquals('Updated Slider', $slider->title);
        $this->assertEquals('https://updated.com', $slider->link);
        $this->assertFalse($slider->is_active); // Should be boolean false
    }

    /** @test */
    public function it_validates_is_active_field_properly()
    {
        $slider = Slider::create([
            'title' => 'Test Slider',
            'link' => 'https://test.com',
            'is_active' => true,
        ]);

        // Test with valid boolean values
        $validData = [
            'title' => 'Valid Slider',
            'link' => 'https://valid.com',
            'is_active' => '1',
        ];

        $response = $this->put(route('admin.sliders.update', $slider), $validData);
        $response->assertRedirect(route('admin.sliders.index'));
        $response->assertSessionHasNoErrors();

        // Test with another valid boolean value
        $validData2 = [
            'title' => 'Valid Slider 2',
            'link' => 'https://valid2.com',
            'is_active' => '0',
        ];

        $response = $this->put(route('admin.sliders.update', $slider), $validData2);
        $response->assertRedirect(route('admin.sliders.index'));
        $response->assertSessionHasNoErrors();
    }

    /** @test */
    public function slider_model_casts_is_active_to_boolean()
    {
        // Test creating with string '1'
        $slider1 = Slider::create([
            'title' => 'Test Slider 1',
            'is_active' => '1',
        ]);

        $this->assertIsBool($slider1->is_active);
        $this->assertTrue($slider1->is_active);

        // Test creating with string '0'
        $slider2 = Slider::create([
            'title' => 'Test Slider 2',
            'is_active' => '0',
        ]);

        $this->assertIsBool($slider2->is_active);
        $this->assertFalse($slider2->is_active);

        // Test creating with actual boolean
        $slider3 = Slider::create([
            'title' => 'Test Slider 3',
            'is_active' => true,
        ]);

        $this->assertIsBool($slider3->is_active);
        $this->assertTrue($slider3->is_active);
    }
}
