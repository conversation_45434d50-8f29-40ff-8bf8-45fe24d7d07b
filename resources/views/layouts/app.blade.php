<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    @include('app.layout.components.analytics.head')
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    @hasSection('meta_title')
        <title>@yield('meta_title')</title>
    @else
        <title>Zoo-mall.pl - Intenetowy slkep zoologiczny.</title>
    @endisset

    @hasSection('meta_description')
        <meta name="description" content="@yield('meta_description')"/>
    @else
        {{-- <meta name="description" content=""/> --}}
    @endisset
    <!-- Vendor Stylesheets -->
    <link rel="stylesheet" type="text/css" href="/assets/css/plugins/bootstrap.min.css?v=3.0.1">
    <link rel="stylesheet" type="text/css" href="/assets/css/plugins/animate.min.css?v=3.0.1">
    <link rel="stylesheet" type="text/css" href="/assets/css/plugins/magnific-popup.css?v=3.0.1">
    <link rel="stylesheet" type="text/css" href="/assets/css/plugins/slick.css?v=3.0.1">
    <link rel="stylesheet" type="text/css" href="/assets/css/plugins/slick-theme.css?v=3.0.1">
    <link rel="stylesheet" type="text/css" href="/assets/css/plugins/ion.rangeSlider.min.css?v=3.0.1">

    <!-- Icon Fonts -->
    <link rel="stylesheet" type="text/css" href="/assets/fonts/flaticon/flaticon.css?v=3.0.1">
    <link rel="stylesheet" type="text/css" href="/assets/fonts/font-awesome/css/all.min.css?v=3.0.1">
    <link rel="stylesheet" type="text/css" href="/assets/css/plugins/jquery-ui.min.css?v=3.0.1">

    <meta name="csrf-token" content="{{ csrf_token() }}">
  
    <!-- Petitdio Style sheet -->
    <link rel="stylesheet" type="text/css" href="/assets/css/style.min.css?v=3.0.1">
    <link rel="stylesheet" type="text/css" href="/assets/css/custom.css">
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon.ico">
    @yield('additional_header')
    @if (app()->environment('production'))
        <meta name="google-site-verification" content="RfoI5AUv3ltOh6l4_j3iNDx6EpFb7iKfAE5e7mxIAC8" />
        @include('app.layout.components.analytics.facebook')
    @endif
</head>

<body>
    @include('app.layout.components.analytics.body')
    
    <!-- Prealoder start -->
    <div class="andro_preloader hidden">
        <div class="spinner">
        <div class="dot1"></div>
        <div class="dot2"></div>
        </div>
    </div>

    @include('app.layout.components.header.mobile')
    
    @stack('filter')
    
    <!-- Prealoader End -->
    <header class="andro_header header-2 can-sticky">
        @include('app.layout.header')
    </header>
    @if (View::hasSection('title'))
        @include('app.layout.title')
    @endif
    @yield('content')

    @include('app.layout.footer')

    @if (session('added_product'))
        @include('app.components.in_cart', ['product' => session('added_product')])
    @endif
    
    <!-- Vendor Scripts -->
    <script src="/assets/js/plugins/jquery-3.4.1.min.js"></script>
    <script src="/assets/js/plugins/popper.min.js"></script>
    <script src="/assets/js/plugins/waypoint.js"></script>
    <script src="/assets/js/plugins/bootstrap.min.js"></script>
    <script src="/assets/js/plugins/jquery.magnific-popup.min.js"></script>
    <script src="/assets/js/plugins/jquery.slimScroll.min.js"></script>
    <script src="/assets/js/plugins/imagesloaded.min.js"></script>
    <script src="/assets/js/plugins/jquery.steps.min.js"></script>
    <script src="/assets/js/plugins/jquery.countdown.min.js"></script>
    <script src="/assets/js/plugins/isotope.pkgd.min.js"></script>
    <script src="/assets/js/plugins/slick.min.js"></script>
    <script src="/assets/js/plugins/ion.rangeSlider.min.js"></script>
    <script src="/assets/js/plugins/jquery.zoom.min.js"></script>
    <script src="/assets/js/plugins/jquery-ui.min.js"></script>
    <script src="/assets/js/plugins/lazysizes.min.js" async></script>

    <script src="/assets/js/plugins/js.cookie.min.js"></script>

    <!-- Utility functions first -->
    <script src="/assets/js/utils.js"></script>
    
    <!-- Petitdio Scripts -->
    <!-- <script src="/assets/js/main.min.js?v=3.0.1"></script> -->
    <script src="/assets/js/main.js"></script>
    
    <!-- Modern app functionality -->
    <script src="/assets/js/app-modern.js"></script>
    
    <!-- Additional scripts -->
    <script src="/assets/js/additional.js"></script>
    
    @stack('additional_scripts')
    <!-- Conditionally load page-specific JS -->
    @if(request()->is('cart*'))
        <script src="/assets/js/cart.js"></script>
    @endif
    
    @if(request()->is('checkout*'))
        <script src="/assets/js/checkout.js"></script>
        <script src="/assets/js/paczkomat.js"></script>
    @endif

    @stack('scripts')
</body>
