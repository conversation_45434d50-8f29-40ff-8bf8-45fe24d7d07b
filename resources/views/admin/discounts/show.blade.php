@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
            <i class="bi bi-percent me-2"></i>Discount Details
        </h2>
        <div class="flex space-x-2">
            <a href="{{ route('admin.discounts.edit', $discount) }}" class="bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
                <i class="bi bi-pencil me-1"></i>Edit Discount
            </a>
            <a href="{{ route('admin.discounts.index') }}" class="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
                <i class="bi bi-arrow-left me-1"></i>Back to List
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Basic Information -->
        <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-4">Discount Information</h3>
            
            <div class="space-y-3">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Discount Percentage</label>
                    <p class="text-2xl font-bold text-green-600">{{ number_format($discount->percentage, 2) }}%</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">Status</label>
                    @if($discount->status === 'active')
                        <span class="px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            <i class="bi bi-check-circle me-1"></i>Active
                        </span>
                    @elseif($discount->status === 'scheduled')
                        <span class="px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                            <i class="bi bi-clock me-1"></i>Scheduled
                        </span>
                    @else
                        <span class="px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                            <i class="bi bi-x-circle me-1"></i>Expired
                        </span>
                    @endif
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">Start Date</label>
                    <p class="text-gray-900">{{ $discount->start_date->format('d.m.Y H:i') }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">End Date</label>
                    <p class="text-gray-900">
                        {{ $discount->end_date ? $discount->end_date->format('d.m.Y H:i') : 'No End Date' }}
                    </p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">Duration</label>
                    <p class="text-gray-900">
                        @if($discount->end_date)
                            {{ $discount->start_date->diffInDays($discount->end_date) }} days
                        @else
                            Indefinite
                        @endif
                    </p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">Created</label>
                    <p class="text-gray-900">{{ $discount->created_at->format('d.m.Y H:i') }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">Last Updated</label>
                    <p class="text-gray-900">{{ $discount->updated_at->format('d.m.Y H:i') }}</p>
                </div>
            </div>
        </div>

        <!-- Products -->
        <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-4">
                Applied Products 
                <span class="text-sm font-normal text-gray-600">({{ $discount->products->count() }})</span>
            </h3>
            
            @if($discount->products->count() > 0)
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    @foreach($discount->products as $product)
                        <div class="flex items-center justify-between p-3 bg-white rounded border">
                            <div class="flex items-center space-x-3">
                                @if($product->hasMedia('images'))
                                    <img src="{{ $product->getFirstMediaUrl('images', 'thumb') }}"
                                         alt="{{ $product->name }}"
                                         class="h-12 w-12 object-cover rounded">
                                @else
                                    <div class="h-12 w-12 bg-gray-100 rounded flex items-center justify-center">
                                        <i class="bi bi-image text-gray-400"></i>
                                    </div>
                                @endif
                                <div>
                                    <p class="font-medium text-gray-900">{{ $product->name }}</p>
                                    <p class="text-sm text-gray-500">SKU: {{ $product->sku }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-medium text-gray-900">{{ number_format($product->price, 2) }} PLN</p>
                                <p class="text-sm text-green-600">
                                    {{ number_format($product->price * (100 - $discount->percentage) / 100, 2) }} PLN
                                </p>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <i class="bi bi-box-seam text-gray-400 text-4xl"></i>
                    <p class="text-gray-500 mt-2">No products assigned to this discount</p>
                    <a href="{{ route('admin.discounts.edit', $discount) }}" class="text-blue-500 hover:text-blue-700 mt-2 inline-block">
                        <i class="bi bi-plus-circle me-1"></i>Assign Products
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Statistics -->
    @if($discount->products->count() > 0)
    <div class="mt-6 bg-gray-50 p-4 rounded-lg">
        <h3 class="text-lg font-semibold mb-4">Statistics</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-blue-600">{{ $discount->products->count() }}</p>
                <p class="text-sm text-gray-600">Products</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-green-600">
                    {{ number_format($discount->products->sum('price'), 2) }} PLN
                </p>
                <p class="text-sm text-gray-600">Original Value</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-orange-600">
                    {{ number_format($discount->products->sum('price') * $discount->percentage / 100, 2) }} PLN
                </p>
                <p class="text-sm text-gray-600">Total Discount</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-purple-600">
                    {{ number_format($discount->products->sum('price') * (100 - $discount->percentage) / 100, 2) }} PLN
                </p>
                <p class="text-sm text-gray-600">Discounted Value</p>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection
