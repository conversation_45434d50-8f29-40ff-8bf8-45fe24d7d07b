<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Discount;
use App\Models\Product;
use Illuminate\Http\Request;

class DiscountController extends AdminController
{
    public function index(Request $request)
    {
        list($discounts, $filters, $sorts) = $this->applyPaginationAndFiltering(Discount::query());

        return view('admin.discounts.index', compact('discounts', 'filters', 'sorts'));
    }

    public function create()
    {
        return view('admin.discounts.create');
    }

    public function show(Discount $discount)
    {
        return view('admin.discounts.show', compact('discount'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'percentage' => 'required|numeric|min:0|max:100',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'product_ids' => 'nullable|string',
        ]);

        $discount = Discount::create($request->only(['percentage', 'start_date', 'end_date']));

        // Attach products
        if ($request->filled('product_ids')) {
            $productIds = array_filter(explode(',', $request->input('product_ids')));
            if (!empty($productIds)) {
                $discount->products()->attach($productIds);
            }
        }

        return redirect()->route('admin.discounts.index')->with('success', 'Discount created successfully.');
    }

    public function edit(Discount $discount)
    {
        return view('admin.discounts.edit', compact('discount'));
    }

    public function update(Request $request, Discount $discount)
    {
        $request->validate([
            'percentage' => 'required|numeric|min:0|max:100',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $discount->update($request->only(['percentage', 'start_date', 'end_date']));

        // Products are now managed via AJAX, so we don't handle them here

        return redirect()->route('admin.discounts.index')->with('success', 'Discount updated successfully.');
    }

    public function destroy(Discount $discount)
    {
        $discount->delete();
        return redirect()->route('admin.discounts.index')->with('success', 'Discount deleted successfully.');
    }

    /**
     * Add a product to the discount
     */
    public function addProduct(Request $request, Discount $discount)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
        ]);

        $product = Product::findOrFail($request->product_id);

        // Check if product is already attached to this discount
        if (!$discount->products()->where('product_id', $product->id)->exists()) {
            $discount->products()->attach($product->id);
        }

        return response()->json(['success' => true, 'message' => 'Product added to discount successfully.']);
    }

    /**
     * Remove a product from the discount
     */
    public function removeProduct(Request $request, Discount $discount)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
        ]);

        $discount->products()->detach($request->product_id);

        return response()->json(['success' => true, 'message' => 'Product removed from discount successfully.']);
    }

    /**
     * Search products for adding to discount
     */
    public function searchProducts(Request $request, Discount $discount)
    {
        $search = $request->get('search', '');
        $page = $request->get('page', 1);
        $perPage = 20;

        $query = Product::with(['category', 'producent', 'media'])
                       ->where(function($q) use ($search) {
                           if ($search) {
                               $q->where('name', 'like', '%' . $search . '%')
                                 ->orWhere('sku', 'like', '%' . $search . '%');
                           }
                       })
                       ->whereDoesntHave('discounts', function($q) use ($discount) {
                           $q->where('discount_id', $discount->id);
                       });

        $products = $query->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'products' => $products->items(),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
                'has_more' => $products->hasMorePages(),
            ]
        ]);
    }
}
