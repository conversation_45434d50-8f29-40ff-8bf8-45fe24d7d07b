@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">Edit Slider</h2>
        <a href="{{ route('admin.sliders.index') }}" class="text-blue-500 hover:text-blue-700">
            &larr; Back to Sliders
        </a>
    </div>

    <form action="{{ route('admin.sliders.update', $slider->id) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')
        <div class="mb-4">
            <label for="title" class="block text-gray-700">Title</label>
            <input type="text" id="title" name="title" class="w-full border rounded p-2" value="{{ $slider->title }}" required>
            @error('title')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>
        <div class="mb-4">
            <label for="link" class="block text-gray-700">Link</label>
            <input type="url" id="link" name="link" class="w-full border rounded p-2" value="{{ $slider->link }}">
            @error('link')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>
        <div class="mb-4">
            <label for="is_active" class="inline-flex items-center">
                <input type="hidden" name="is_active" value="0">
                <input type="checkbox" id="is_active" name="is_active" class="form-checkbox" value="1" {{ $slider->is_active ? 'checked' : '' }}>
                <span class="ml-2 text-gray-700">Active</span>
            </label>
            @error('is_active')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>
        <div class="mb-4">
            <label for="start_date" class="block text-gray-700">Start Date</label>
            <input type="date" id="start_date" name="start_date" class="w-full border rounded p-2" value="{{ $slider->start_date }}">
            @error('start_date')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>
        <div class="mb-4">
            <label for="end_date" class="block text-gray-700">End Date</label>
            <input type="date" id="end_date" name="end_date" class="w-full border rounded p-2" value="{{ $slider->end_date }}">
            @error('end_date')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>
        <div class="mb-4">
            <label for="image" class="block text-gray-700">Image</label>
            <input type="file" id="image" name="image" class="w-full border rounded p-2">
            @if($slider->hasMedia('image'))
                <img src="{{ $slider->getFirstMediaUrl('image', 'thumb') }}" alt="{{ $slider->title }}" class="mt-2 h-24">
            @endif
            @error('image')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>
        <div class="mb-4">
            <label for="mobile_image" class="block text-gray-700">Mobile Image</label>
            <input type="file" id="mobile_image" name="mobile_image" class="w-full border rounded p-2">
            @if($slider->hasMedia('mobile_image'))
                <img src="{{ $slider->getFirstMediaUrl('mobile_image', 'thumb') }}" alt="{{ $slider->title }} Mobile" class="mt-2 h-24">
            @endif
            @error('mobile_image')
                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
            @enderror
        </div>
        <div class="flex justify-between">
            <a href="{{ route('admin.sliders.index') }}" class="bg-gray-300 text-gray-700 py-2 px-4 rounded">Cancel</a>
            <button type="submit" name="action" value="update" class="bg-blue-500 text-white py-2 px-4 rounded">Update</button>
            <button type="submit" name="action" value="save" class="bg-green-500 text-white py-2 px-4 rounded">Save</button>
        </div>
    </form>
</div>
@endsection
