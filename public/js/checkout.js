// Initialize the delivery method display on page load
document.addEventListener('DOMContentLoaded', function() {
    const selectedMethod = document.querySelector('input[name="delivery_method"]:checked');
    if (selectedMethod) {
        toggleDeliveryMethod(selectedMethod);
    }
});

// Toggle between delivery methods (courier vs paczkomat)
function toggleDeliveryMethod(radio) {
    const paczkomatSection = document.getElementById('paczkomat-section');
    
    if (radio.value === 'inpost_paczkomat') {
        paczkomatSection.style.display = 'block';
    } else {
        paczkomatSection.style.display = 'none';
    }
}

// Toggle new address form visibility
function toggleNewAddressForm(show) {
    const newAddressForm = document.getElementById('new-address-form');
    if (newAddressForm) {
        newAddressForm.style.display = show ? 'block' : 'none';
    }
}

// Handle paczkomat map
function openPaczkomatMap() {
    const mapContainer = document.getElementById('easypack-map');
    mapContainer.style.display = 'block';
    
    // If the map widget needs to be refreshed
    if (window.easyPack && typeof window.easyPack.mapWidget === 'function') {
        window.easyPack.mapWidget('easypack-map', function(point) {
            setPachkomatPointFromMap(point, true);
        });
    }
}

// Handle paczkomat selection from map
function setPachkomatPointFromMap(point, closeModal = true) {
    const selectedPaczkomatInput = document.getElementById('selected-paczkomat');
    const paczkomatIdInput = document.getElementById('paczkomat-id');
    const paczkomatAddressInput = document.getElementById('paczkomat-address');
    
    const address = `${point.address.line1}, ${point.address.line2}`;
    selectedPaczkomatInput.value = `${point.name} - ${address}`;
    paczkomatIdInput.value = point.name;
    paczkomatAddressInput.value = address;
    
    // Reset saved paczkomat selection if exists
    const savedPaczkomatRadio = document.querySelector('input[name="paczkomat_address_id"]:checked');
    if (savedPaczkomatRadio && savedPaczkomatRadio.value !== 'new') {
        savedPaczkomatRadio.checked = false;
        const newPaczkomatRadio = document.getElementById('new_paczkomat');
        if (newPaczkomatRadio) {
            newPaczkomatRadio.checked = true;
        }
    }
    
    if (closeModal) {
        const mapContainer = document.getElementById('easypack-map');
        mapContainer.style.display = 'none';
    }
}

// Handle selection of saved paczkomat
function selectSavedPaczkomat(paczkomatId) {
    // Hide the map if it's open
    const mapContainer = document.getElementById('easypack-map');
    mapContainer.style.display = 'none';
    
    // Clear the new paczkomat selection fields
    const selectedPaczkomatInput = document.getElementById('selected-paczkomat');
    const paczkomatIdInput = document.getElementById('paczkomat-id');
    
    selectedPaczkomatInput.value = '';
    paczkomatIdInput.value = paczkomatId;
}

// Toggle billing address form
function toggleBillingAddress(checkbox) {
    const billingForm = document.getElementById('billing-address-form');
    const differentBillingInput = document.getElementById('different_billing');

    if (checkbox.checked) {
        // Same as delivery - hide billing form
        billingForm.style.display = 'none';
        billingForm.classList.add('d-none');
        if (differentBillingInput) {
            differentBillingInput.value = '0';
        }

        // Clear required attributes when hidden
        const requiredFields = billingForm.querySelectorAll('input[required]');
        requiredFields.forEach(field => {
            field.removeAttribute('required');
            field.setAttribute('data-was-required', 'true');
        });
    } else {
        // Different billing address - show billing form
        billingForm.style.display = 'block';
        billingForm.classList.remove('d-none');
        if (differentBillingInput) {
            differentBillingInput.value = '1';
        }

        // Restore required attributes when shown
        const wasRequiredFields = billingForm.querySelectorAll('input[data-was-required]');
        wasRequiredFields.forEach(field => {
            field.setAttribute('required', 'required');
            field.removeAttribute('data-was-required');
        });
    }
}

// Initialize form state based on saved values (if any)
document.addEventListener('DOMContentLoaded', function() {
    // Check if there's a saved delivery method
    const savedMethod = document.querySelector('input[name="delivery_method"]:checked');
    if (savedMethod) {
        toggleDeliveryMethod(savedMethod);
    }

    // Check if there's a saved address selection
    const savedAddress = document.querySelector('input[name="delivery_address_id"]:checked');
    if (savedAddress && savedAddress.value === 'new') {
        toggleNewAddressForm(true);
    }

    // Initialize billing address form
    const sameAsDeliveryCheckbox = document.getElementById('same_as_delivery');
    if (sameAsDeliveryCheckbox) {
        toggleBillingAddress(sameAsDeliveryCheckbox);

        // Add event listener for changes
        sameAsDeliveryCheckbox.addEventListener('change', function() {
            toggleBillingAddress(this);
        });
    }
});
