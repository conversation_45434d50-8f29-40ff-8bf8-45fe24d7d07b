<div class="billing-address-section mt-4">
    <h5>Adres do faktury</h5>

    <div class="form-group mb-3">
        <div class="custom-control custom-checkbox">
            <input type="checkbox" class="custom-control-input"
                id="same_as_delivery" name="same_as_delivery" value="1"
                {{ old('same_as_delivery', true) ? 'checked' : '' }}
                onchange="toggleBillingAddress(this)">
            <label class="custom-control-label" for="same_as_delivery">
                Adres na fakturze taki sam jak adres dostawy
            </label>
        </div>
    </div>

    <!-- Hidden field to indicate different billing address -->
    <input type="hidden" id="different_billing" name="different_billing" value="{{ old('same_as_delivery', true) ? '0' : '1' }}">

    <div id="billing-address-form" class="row {{ old('same_as_delivery', true) ? 'd-none' : '' }}">
        <div class="form-group col-xl-12">
            <label for="billing_name">Nazwa adresu</label>
            <input type="text" class="form-control @error('billing_name') is-invalid @enderror"
                id="billing_name" name="billing_name" value="{{ old('billing_name') }}"
                placeholder="np. Dom, Praca">
            @error('billing_name')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
            @enderror
        </div>

        <div class="form-group col-xl-4">
            <label for="billing_post_code">Kod pocztowy <span class="text-danger">*</span></label>
            <input type="text" class="form-control @error('billing_post_code') is-invalid @enderror"
                id="billing_post_code" name="billing_post_code" value="{{ old('billing_post_code') }}"
                placeholder="00-000" required>
            @error('billing_post_code')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
            @enderror
        </div>

        <div class="form-group col-xl-8">
            <label for="billing_city">Miasto <span class="text-danger">*</span></label>
            <input type="text" class="form-control @error('billing_city') is-invalid @enderror"
                id="billing_city" name="billing_city" value="{{ old('billing_city') }}" required>
            @error('billing_city')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
            @enderror
        </div>

        <div class="form-group col-xl-8">
            <label for="billing_street">Ulica <span class="text-danger">*</span></label>
            <input type="text" class="form-control @error('billing_street') is-invalid @enderror"
                id="billing_street" name="billing_street" value="{{ old('billing_street') }}" required>
            @error('billing_street')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
            @enderror
        </div>

        <div class="form-group col-xl-2">
            <label for="billing_building_number">Nr domu <span class="text-danger">*</span></label>
            <input type="text" class="form-control @error('billing_building_number') is-invalid @enderror"
                id="billing_building_number" name="billing_building_number" value="{{ old('billing_building_number') }}" required>
            @error('billing_building_number')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
            @enderror
        </div>

        <div class="form-group col-xl-2">
            <label for="billing_apartment_number">Nr lokalu</label>
            <input type="text" class="form-control @error('billing_apartment_number') is-invalid @enderror"
                id="billing_apartment_number" name="billing_apartment_number" value="{{ old('billing_apartment_number') }}">
            @error('billing_apartment_number')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
            @enderror
        </div>
    </div>
</div>

@push('scripts')
<script>
function toggleBillingAddress(checkbox) {
    const billingForm = document.getElementById('billing-address-form');
    const differentBillingInput = document.getElementById('different_billing');

    if (checkbox.checked) {
        // Same as delivery - hide billing form
        billingForm.classList.add('d-none');
        differentBillingInput.value = '0';

        // Clear required attributes when hidden
        const requiredFields = billingForm.querySelectorAll('input[required]');
        requiredFields.forEach(field => {
            field.removeAttribute('required');
            field.setAttribute('data-was-required', 'true');
        });
    } else {
        // Different billing address - show billing form
        billingForm.classList.remove('d-none');
        differentBillingInput.value = '1';

        // Restore required attributes when shown
        const wasRequiredFields = billingForm.querySelectorAll('input[data-was-required]');
        wasRequiredFields.forEach(field => {
            field.setAttribute('required', 'required');
            field.removeAttribute('data-was-required');
        });
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    const checkbox = document.getElementById('same_as_delivery');
    if (checkbox) {
        toggleBillingAddress(checkbox);
    }
});
</script>
@endpush