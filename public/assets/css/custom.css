/* Cart notification */
#cart-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 9999;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-20px);
    opacity: 0;
    transition: all 0.3s ease;
}

#cart-notification.show {
    transform: translateY(0);
    opacity: 1;
}

#cart-notification.success {
    background-color: #4CAF50;
    color: white;
}

#cart-notification.error {
    background-color: #F44336;
    color: white;
}

/* Cart icon animation */
.andro_header-cart.pulse {
    animation: cart-pulse 0.7s ease;
}

@keyframes cart-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Mobile Cart Optimizations */
@media (max-width: 768px) {
    /* Hide desktop table completely on mobile */
    .andro_responsive-table.cart_table {
        display: none !important;
    }

    /* Mobile cart items - independent from table */
    .mobile-cart-item {
        background: #fff;
        border-radius: 8px;
        margin-bottom: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid #f0f2f3;
        padding: 15px;
        position: relative;
    }

    /* Mobile cart item header with product info and delete button */
    .mobile-cart-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
        position: relative;
    }

    .mobile-cart-image {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 6px;
        flex-shrink: 0;
    }

    .mobile-cart-product-info {
        flex: 1;
        min-width: 0;
    }

    .mobile-cart-product-name {
        font-size: 14px;
        line-height: 1.3;
        margin-bottom: 0;
        font-weight: 600;
    }

    .mobile-cart-product-name a {
        color: #18181d;
        text-decoration: none;
    }

    .mobile-cart-product-name a:hover {
        color: #fab725;
    }

    .mobile-cart-delete {
        position: absolute;
        top: -5px;
        right: -5px;
        width: 32px;
        height: 32px;
        min-width: 44px;
        min-height: 44px;
        background: #fff;
        border: 2px solid #dc3545;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 2px 6px rgba(220, 53, 69, 0.2);
        padding: 0;
    }

    .mobile-cart-delete:hover {
        background: #dc3545;
        border-color: #dc3545;
        transform: scale(1.05);
        box-shadow: 0 3px 8px rgba(220, 53, 69, 0.3);
    }

    .mobile-cart-delete:focus {
        outline: 2px solid #dc3545;
        outline-offset: 2px;
    }

    .mobile-cart-delete span {
        position: absolute;
        display: block;
        width: 14px;
        height: 2px;
        background: #dc3545;
        border-radius: 1px;
        transition: background-color 0.2s ease;
    }

    .mobile-cart-delete:hover span {
        background: #fff;
    }

    .mobile-cart-delete span:first-child {
        transform: rotate(45deg);
    }

    .mobile-cart-delete span:last-child {
        transform: rotate(-45deg);
    }

    /* Mobile cart controls - price, quantity, total */
    .mobile-cart-controls {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;
        padding-top: 12px;
        border-top: 1px solid #f0f2f3;
    }

    .mobile-price {
        font-size: 14px;
        font-weight: 600;
        color: #18181d;
    }

    .mobile-price .original-price {
        font-size: 12px;
        color: #fab725;
        text-decoration: line-through;
        font-weight: 400;
        margin-right: 4px;
    }

    /* Mobile quantity controls */
    .mobile-qty-control {
        display: flex;
        align-items: center;
        gap: 8px;
        background: #f8f9fa;
        border-radius: 20px;
        padding: 4px 8px;
    }

    .mobile-qty-control .qty-subtract,
    .mobile-qty-control .qty-add {
        width: 32px;
        height: 32px;
        min-width: 44px;
        min-height: 44px;
        border-radius: 50%;
        background: #fff;
        border: 1px solid #dee2e6;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        padding: 0;
    }

    .mobile-qty-control .qty-subtract:hover,
    .mobile-qty-control .qty-add:hover {
        background: #fab725;
        border-color: #fab725;
        color: #fff;
    }

    .mobile-qty-control input {
        width: 40px;
        text-align: center;
        border: none;
        background: transparent;
        font-weight: 600;
        font-size: 14px;
    }

    .mobile-total {
        font-size: 16px;
        font-weight: 700;
        color: #18181d;
    }

    /* Responsive adjustments for very small screens */
    @media (max-width: 375px) {
        .mobile-cart-image {
            width: 50px;
            height: 50px;
        }

        .mobile-cart-controls {
            gap: 8px;
        }

        .mobile-qty-control {
            padding: 3px 6px;
        }

        .mobile-qty-control .qty-subtract,
        .mobile-qty-control .qty-add {
            width: 28px;
            height: 28px;
            min-width: 44px;
            min-height: 44px;
        }

        .mobile-qty-control input {
            width: 35px;
            font-size: 13px;
        }

        .mobile-cart-delete {
            width: 28px;
            height: 28px;
            min-width: 44px;
            min-height: 44px;
        }

        .mobile-cart-delete span {
            width: 12px;
            height: 2px;
        }
    }

    /* Mobile cart summary optimizations */
    .andro_cart-form .col-lg-6 {
        margin-top: 20px;
    }

    .andro_cart-form table {
        margin-bottom: 20px;
    }

    .andro_cart-form table th,
    .andro_cart-form table td {
        padding: 12px 15px;
        font-size: 14px;
    }

    .andro_cart-form .andro_btn-custom {
        padding: 12px 20px;
        font-size: 16px;
        font-weight: 600;
    }

    /* Accessibility improvements */

    .andro_responsive-table.cart_table .close-btn {
        min-width: 44px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Focus states for better accessibility */
    .mobile-qty-control .qty-subtract:focus,
    .mobile-qty-control .qty-add:focus,
    .andro_responsive-table.cart_table .close-btn:focus {
        outline: 2px solid #fab725;
        outline-offset: 2px;
    }

    .mobile-qty-control input:focus {
        outline: 2px solid #fab725;
        outline-offset: 1px;
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .andro_responsive-table.cart_table tr {
            border: 2px solid #000;
        }

        .mobile-qty-control .qty-subtract,
        .mobile-qty-control .qty-add {
            border: 2px solid #000;
        }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .mobile-qty-control .qty-subtract,
        .mobile-qty-control .qty-add {
            transition: none;
        }
    }
}

/* Loading state for buttons */
.andro_btn-custom.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Mobile Cart Summary - Fixed Bottom Positioning */
@media (max-width: 768px) {
    .cart-summary-container {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        border-top: 2px solid #f0f2f3;
        box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
        padding: 15px 20px;
        z-index: 1000;
        max-height: 50vh;
        overflow-y: auto;
    }

    .cart-summary-container table {
        margin-bottom: 15px;
        width: 100%;
    }

    .cart-summary-container table th,
    .cart-summary-container table td {
        padding: 8px 12px;
        font-size: 14px;
        border: none;
        background: transparent;
    }

    .cart-summary-container table th {
        font-weight: 600;
        color: #18181d;
        text-align: left;
        width: 40%;
    }

    .cart-summary-container table td {
        text-align: right;
        font-weight: 600;
        width: 60%;
    }

    .cart-summary-container .andro_btn-custom {
        width: 100%;
        padding: 12px 20px;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 0;
    }
}

/* Desktop Cart Summary - Preserve Existing Layout */
@media (min-width: 769px) {
    .cart-summary-container {
        position: static;
        background: transparent;
        border-top: none;
        box-shadow: none;
        padding: 0;
        z-index: auto;
        max-height: none;
        overflow-y: visible;
    }

    .cart-summary-container table th,
    .cart-summary-container table td {
        padding: 15px;
        font-size: inherit;
        border: 1px solid #f0f2f3;
        background-color: #fff;
    }

    .cart-summary-container table th {
        width: auto;
        text-align: left;
        font-weight: 600;
        color: #18181d;
        font-family: "Montserrat", sans-serif;
    }

    .cart-summary-container table td {
        width: auto;
        text-align: left;
    }

    .mobile-cart-item:last-child {
        margin-bottom: 12px; /* Reset mobile spacing */
    }

    .section.pb-2 {
        padding-bottom: 15px !important; /* Reset mobile padding */
    }
}