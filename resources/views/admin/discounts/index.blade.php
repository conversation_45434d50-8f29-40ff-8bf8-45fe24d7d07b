@extends('admin.layout')

@section('content')
<div class="bg-white p-6 rounded shadow-md">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
            <i class="bi bi-percent me-2"></i>Discounts Management
        </h2>
        <div class="flex space-x-2">
            <a href="{{ route('admin.discounts.create') }}" class="bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
                <i class="bi bi-plus-circle me-1"></i>Add Discount
            </a>
            <button type="button" id="refreshBtn" class="bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600">
                <i class="bi bi-arrow-clockwise me-1"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="mb-4 p-4 bg-gray-50 rounded-lg">
        <form method="GET" action="{{ route('admin.discounts.index') }}" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Percentage From -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Percentage From</label>
                    <input type="number"
                           name="percentage_from"
                           value="{{ request('percentage_from') }}"
                           placeholder="Min percentage..."
                           class="border rounded p-2 w-full"
                           step="0.01"
                           min="0"
                           max="100">
                </div>

                <!-- Percentage To -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Percentage To</label>
                    <input type="number"
                           name="percentage_to"
                           value="{{ request('percentage_to') }}"
                           placeholder="Max percentage..."
                           class="border rounded p-2 w-full"
                           step="0.01"
                           min="0"
                           max="100">
                </div>

                <!-- Start Date From -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Start Date From</label>
                    <input type="date"
                           name="start_date_from"
                           value="{{ request('start_date_from') }}"
                           class="border rounded p-2 w-full">
                </div>

                <!-- Start Date To -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Start Date To</label>
                    <input type="date"
                           name="start_date_to"
                           value="{{ request('start_date_to') }}"
                           class="border rounded p-2 w-full">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- End Date From -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">End Date From</label>
                    <input type="date"
                           name="end_date_from"
                           value="{{ request('end_date_from') }}"
                           class="border rounded p-2 w-full">
                </div>

                <!-- End Date To -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">End Date To</label>
                    <input type="date"
                           name="end_date_to"
                           value="{{ request('end_date_to') }}"
                           class="border rounded p-2 w-full">
                </div>

                <!-- Created Date From -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Created From</label>
                    <input type="date"
                           name="created_at_from"
                           value="{{ request('created_at_from') }}"
                           class="border rounded p-2 w-full">
                </div>

                <!-- Created Date To -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Created To</label>
                    <input type="date"
                           name="created_at_to"
                           value="{{ request('created_at_to') }}"
                           class="border rounded p-2 w-full">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Items per page -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Items per page</label>
                    <select name="per_page" class="border rounded p-2 w-full">
                        <option value="15" {{ request('per_page', 15) == 15 ? 'selected' : '' }}>15</option>
                        <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25</option>
                        <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                    </select>
                </div>
            </div>

            <div class="flex space-x-2">
                <button type="submit" class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                    <i class="bi bi-funnel me-1"></i>Filter
                </button>
                <a href="{{ route('admin.discounts.index') }}" class="bg-gray-300 text-black py-2 px-4 rounded hover:bg-gray-400">
                    <i class="bi bi-x-circle me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full bg-white mt-4">
            <thead>
                <tr class="bg-gray-50">
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'percentage', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Discount Percentage
                            @if(request('sort') === 'percentage')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'start_date', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Start Date
                            @if(request('sort') === 'start_date')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'end_date', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            End Date
                            @if(request('sort') === 'end_date')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-center">Status</th>
                    <th class="py-3 px-4 border-b text-center">Products</th>
                    <th class="py-3 px-4 border-b text-left">
                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'created_at', 'direction' => request('direction') === 'asc' ? 'desc' : 'asc']) }}"
                           class="text-gray-700 hover:text-gray-900">
                            Created
                            @if(request('sort') === 'created_at')
                                <i class="bi bi-arrow-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1"></i>
                            @endif
                        </a>
                    </th>
                    <th class="py-3 px-4 border-b text-center">Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($discounts as $discount)
                <tr class="hover:bg-gray-50">
                    <td class="py-3 px-4 border-b">
                        <div class="font-medium text-lg">{{ number_format($discount->percentage, 2) }}%</div>
                        <div class="text-sm text-gray-500">
                            {{ $discount->products->count() }} product{{ $discount->products->count() !== 1 ? 's' : '' }}
                        </div>
                    </td>
                    <td class="py-3 px-4 border-b text-sm text-gray-900">
                        {{ $discount->start_date->format('d.m.Y H:i') }}
                    </td>
                    <td class="py-3 px-4 border-b text-sm text-gray-900">
                        {{ $discount->end_date ? $discount->end_date->format('d.m.Y H:i') : 'No End Date' }}
                    </td>
                    <td class="py-3 px-4 border-b text-center">
                        @if($discount->status === 'active')
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800" title="Currently active">
                                <i class="bi bi-check-circle me-1"></i>Active
                            </span>
                        @elseif($discount->status === 'scheduled')
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800" title="Scheduled for future">
                                <i class="bi bi-clock me-1"></i>Scheduled
                            </span>
                        @else
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800" title="Expired">
                                <i class="bi bi-x-circle me-1"></i>Expired
                            </span>
                        @endif
                    </td>
                    <td class="py-3 px-4 border-b text-center">
                        @if($discount->products->count() > 0)
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="bi bi-box-seam me-1"></i>{{ $discount->products->count() }}
                            </span>
                        @else
                            <span class="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                <i class="bi bi-dash me-1"></i>None
                            </span>
                        @endif
                    </td>
                    <td class="py-3 px-4 border-b text-sm text-gray-500">
                        {{ $discount->created_at->format('d.m.Y H:i') }}
                    </td>
                    <td class="py-3 px-4 border-b text-center">
                        <div class="flex justify-center space-x-2">
                            <a href="{{ route('admin.discounts.show', $discount) }}"
                               class="text-blue-500 hover:text-blue-700 text-sm"
                               title="View Discount">
                                <i class="bi bi-eye"></i>
                            </a>
                            <a href="{{ route('admin.discounts.edit', $discount) }}"
                               class="text-green-500 hover:text-green-700 text-sm"
                               title="Edit Discount">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <form action="{{ route('admin.discounts.destroy', $discount) }}"
                                  method="POST"
                                  class="inline-block delete-form">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        class="text-red-500 hover:text-red-700 text-sm"
                                        title="Delete Discount">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="7" class="py-8 px-4 text-center text-gray-500">
                        No discounts found matching your criteria.
                        <a href="{{ route('admin.discounts.create') }}" class="text-blue-500 hover:text-blue-700 ml-2">
                            <i class="bi bi-plus-circle me-1"></i>Add first discount
                        </a>
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    @if($discounts->hasPages())
        <div class="mt-6">
            {{ $discounts->links() }}
        </div>
    @endif

    <!-- Summary -->
    <div class="mt-6 bg-gray-50 p-4 rounded-lg">
        <p class="text-sm text-gray-600">
            Showing {{ $discounts->firstItem() ?? 0 }} to {{ $discounts->lastItem() ?? 0 }}
            of {{ $discounts->total() }} discounts
        </p>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const refreshBtn = document.getElementById('refreshBtn');

    // Refresh functionality
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>Refreshing...';

            setTimeout(() => {
                location.reload();
            }, 500);
        });
    }

    // Handle delete forms
    document.querySelectorAll('.delete-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            if (confirm('Are you sure you want to delete this discount? This action cannot be undone.')) {
                this.submit();
            }
        });
    });
});
</script>
@endpush
@endsection
